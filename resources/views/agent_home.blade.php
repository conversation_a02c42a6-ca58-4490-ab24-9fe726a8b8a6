@extends('layouts.app')

@section('content')
<div class="container-fluid">

    <div class="d-flex flex-wrap gap-2 mb-3">
        <button type="button" class="btn btn-sm btn-warning text-white" data-bs-toggle="modal" data-bs-target="#record_votes">
            <i class="bi bi-pencil-square"></i> <span class="button-text">Record Votes</span>
        </button>

        <button type="button" class="btn btn-sm btn-warning text-white" data-bs-toggle="modal" data-bs-target="#upload_evedence">
            <i class="bi bi-upload"></i> <span class="button-text">Upload Evidence</span>
        </button>
        
        <button type="button" class="btn btn-sm btn-danger text-white" data-bs-toggle="modal" data-bs-target="#record_spoiled_votes">
            <i class="bi bi-x-circle"></i> <span class="button-text">Record Spoiled Votes</span>
        </button>
    </div>
    <hr>   
 
    <div class="station-card">
        <div class="station-header flex-wrap">
            <h5 class="station-name mb-2 mb-sm-0">
                <i class="bi bi-building"></i>
                {{ $agent->polling_station->name }} Polling station
            </h5>
            <span class="badge bg-light text-dark">Station #{{ $agent->polling_station_id }}</span>
        </div>
        <div class="station-body">
            @if($agent)
            <div class="agent-info flex-wrap">
                <div class="agent-icon">
                    <i class="bi bi-person-vcard"></i>
                </div>
                <div class="agent-details">
                    <div class="agent-name">{{ $agent->user->name }}</div>
                    <div class="agent-phone">
                        <i class="bi bi-telephone me-1 small"></i>
                        {{ $agent->user->phone_number }}
                    </div>
                </div>
            </div>           
           @endif
            
            @foreach ($positions as $position)
            @php
                $totalVotes = $position->totalStationVotes($agent->polling_station->id);
            @endphp
            <div class="position-section position-results mt-4">
                <h6 class="mb-3"><i class="bi bi-award me-2 text-warning"></i>{{ $position->name }}</h6>
                
                <div class="table-responsive">
                    <table class="table table-sm position-votes-table" data-position-id="{{ $position->id }}">
                        <thead class="table-light">
                            <tr>
                                <th>Candidate</th>
                                <th class="text-center">Votes</th>
                                <th class="text-end">Percentage</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($position->candidates as $candidate)
                            @php
                                $candidateVotes = $candidate->totalStationVotes($agent->polling_station->id);
                                $percentage = ($totalVotes > 0) ? round(($candidateVotes/$totalVotes * 100), 1) : 0;
                            @endphp
                            <tr>
                                <td>{{ $candidate->name }}</td>
                                <td class="text-center">{{ $candidateVotes }}</td>
                                <td class="text-end">
                                    <span class="badge" style="background: linear-gradient(135deg, #FFA500 0%, #FFD700 100%)">
                                        {{ $percentage }}%
                                    </span>
                                </td>
                            </tr>
                            @endforeach
                        
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th>TOTAL</th>
                                <th class="text-center">{{ $totalVotes }}</th>
                                <th class="text-end">100%</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            @endforeach
            
            <!-- Spoiled Votes Section -->
            <div class="position-section position-results mt-4">
                <h6 class="mb-3"><i class="bi bi-x-circle me-2 text-danger"></i>Spoiled Votes</h6>
                
                <div class="table-responsive">
                    <table class="table table-sm spoiled-votes-table">
                        <thead class="table-light">
                            <tr>
                                <th>Position</th>
                                <th class="text-center">Spoiled Votes</th>
                                <th class="text-end">Recorded On</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $spoiledVotes = \App\Models\SpoiledVote::where('polling_station_id', $agent->polling_station_id)
                                    ->with('position')
                                    ->get();
                                $totalSpoiled = $spoiledVotes->sum('number_of_votes');
                            @endphp
                            
                            @forelse ($spoiledVotes as $spoiledVote)
                            <tr>
                                <td>{{ $spoiledVote->position->name }}</td>
                                <td class="text-center">
                                    <span class="badge bg-danger">{{ $spoiledVote->number_of_votes }}</span>
                                </td>
                                <td class="text-end">
                                    <small class="text-muted">{{ $spoiledVote->created_at->format('M d, H:i') }}</small>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="3" class="text-center py-3">
                                    <span class="text-muted">No spoiled votes recorded yet</span>
                                </td>
                            </tr>
                            @endforelse
                        </tbody>
                        @if($totalSpoiled > 0)
                        <tfoot class="table-light">
                            <tr>
                                <th>TOTAL</th>
                                <th class="text-center">{{ $totalSpoiled }}</th>
                                <th class="text-end"></th>
                            </tr>
                        </tfoot>
                        @endif
                    </table>
                </div>
            </div>

             @if(count($agent->eveidences) > 0)
            <div class="evidence-list flex-wrap">
                @foreach ($agent->eveidences as $evidence)
                <a href="{{ asset('files/'.$evidence->file_url) }}" target="_blank" class="evidence-item mb-2">
                    <i class="bi bi-file-earmark-text"></i>
                    {{ $evidence->file_name }}
                </a>
                @endforeach
            </div>
            @endif
        </div>
    </div>
       


    <div class="modal fade" id="record_votes" tabindex="-1" aria-labelledby="agentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-fullscreen-sm-down">
        <div class="modal-content modal-content-custom">
        <div class="modal-header modal-header-custom">
            <h5 class="modal-title modal-title-custom" id="agentModalLabel">
                <i class="bi bi-person-plus me-2"></i> Record Votes
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body modal-body-custom">
            <form action="{{ route('votes.store') }}" method="post">
                @csrf
            
                
                <div class="form-group">
                    <label for="name" class="form-label">Select position <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-person"></i>
                        </span>

                        <select name="position_id" id="position_id" class="form-control form-control-custom">
                            <option value=""></option>
                            @foreach ($positions as $position)
                                <option value="{{ $position->id }}">{{ $position->name }}</option>                            
                            @endforeach
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="phone_number" class="form-label">Select Candidate <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-phone"></i>
                        </span>
                        <select name="candidate_id" id="candidate_id" class="form-control form-control-custom">
                        </select>
                    </div>                
                </div>

                <div class="form-group">
                    <label for="phone_number" class="form-label">Number of Votes <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-phone"></i>
                        </span>
                        <input type="number" name="number_of_votes" class="form-control form-control-custom">
                    </div>                
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-save">
                        <i class="bi bi-check-circle me-1"></i> Save Vote
                    </button>
                </div>
            </form>
        </div>
        </div>
    </div>
</div>


<div class="modal fade" id="upload_evedence" tabindex="-1" aria-labelledby="agentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-fullscreen-sm-down">
        <div class="modal-content modal-content-custom">
        <div class="modal-header modal-header-custom">
            <h5 class="modal-title modal-title-custom" id="agentModalLabel">
                <i class="bi bi-person-plus me-2"></i> Upload Evedence
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body modal-body-custom">
            <form action="{{ route('evedence.store') }}" method="post" enctype="multipart/form-data">
                @csrf           
                
                <div class="form-group">
                    <label for="name" class="form-label">Select File <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-person"></i>
                        </span>
                        <input type="file" name="picture">                        
                    </div>
                </div>                

                <div class="form-group">
                    <label for="phone_number" class="form-label">File Name <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-phone"></i>
                        </span>
                        <input type="text" name="file_name" class="form-control form-control-custom">
                    </div>                
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-save">
                        <i class="bi bi-check-circle me-1"></i> Save File
                    </button>
                </div>
            </form>
        </div>
        </div>
    </div>
    </div>

<!-- Modal for Recording Spoiled Votes -->
<div class="modal fade" id="record_spoiled_votes" tabindex="-1" aria-labelledby="spoiledVotesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-fullscreen-sm-down">
        <div class="modal-content modal-content-custom">
            <div class="modal-header modal-header-custom bg-danger text-white">
                <h5 class="modal-title modal-title-custom" id="spoiledVotesModalLabel">
                    <i class="bi bi-x-circle me-2"></i> Record Spoiled Votes
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body modal-body-custom">
                <form action="{{ route('spoiled_votes.store') }}" method="post">
                    @csrf
                    
                    <!-- Hidden field for polling station ID -->
                    <input type="hidden" name="polling_station_id" value="{{ $agent->polling_station_id }}">
                    
                    <div class="form-group mb-3">
                        <label for="position_id" class="form-label">Select Position <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-award"></i>
                            </span>
                            <select name="position_id" id="spoiled_position_id" class="form-control form-control-custom" required>
                                <option value="">Select a position</option>
                                @foreach ($positions as $position)
                                    <option value="{{ $position->id }}">{{ $position->name }}</option>                            
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="number_of_votes" class="form-label">Number of Spoiled Votes <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-hash"></i>
                            </span>
                            <input type="number" name="number_of_votes" class="form-control form-control-custom" min="0" required>
                        </div>                
                    </div>
                    
                    <div class="form-group mb-3">
                        <label for="remarks" class="form-label">Remarks (Optional)</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-chat-left-text"></i>
                            </span>
                            <textarea name="remarks" class="form-control form-control-custom" rows="3" placeholder="Describe the reason for spoiled votes (optional)"></textarea>
                        </div>                
                    </div>

                    <div class="d-flex justify-content-end gap-2 mt-4">
                        <button type="button" class="btn btn-cancel" data-bs-dismiss="modal">
                            <i class="bi bi-x-circle me-1"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-save me-1"></i> Record Spoiled Votes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
    <link rel="stylesheet" href="{{ asset('css/custom.css') }}">
    <link rel="stylesheet" href="{{ asset('css/mobile.css') }}">
@endsection

@push('scripts')
   <script type="text/javascript">

    document.getElementById('position_id').addEventListener('change', function(e) {
        var position_id = e.target.value;

        fetch('/ajax_get_candidates/' + position_id)
            .then(response => response.json())
            .then(data => {
                var candidateSelect = document.getElementById('candidate_id');
                candidateSelect.innerHTML = ''; // Clear existing options

                var emptyOption = document.createElement('option');
                candidateSelect.appendChild(emptyOption); // Add empty option

                data.forEach(function(subObject) {
                    var option = document.createElement('option');
                    option.value = subObject.id;
                    option.textContent = subObject.name;
                    candidateSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error fetching candidates:', error);
            });
    });

    </script>    
@endpush

