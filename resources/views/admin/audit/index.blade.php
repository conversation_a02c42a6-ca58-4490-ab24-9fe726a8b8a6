@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header-gold">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 text-white">
                                <i class="bi bi-shield-check me-2"></i>
                                Vote Audit Dashboard
                            </h2>
                            <p class="text-white opacity-75 mb-0">Monitor and review all vote submissions for transparency and accountability</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{{ route('admin.audit.activity') }}" class="btn btn-gold-outline">
                                <i class="bi bi-activity me-1"></i>
                                Live Activity
                            </a>
                            <a href="{{ route('admin.audit.statistics') }}" class="btn btn-gold-outline">
                                <i class="bi bi-graph-up me-1"></i>
                                Statistics
                            </a>
                            <a href="{{ route('admin.audit.flagged') }}" class="btn btn-gold-outline">
                                <i class="bi bi-flag me-1"></i>
                                Flagged ({{ $suspiciousActivity['total_flagged'] }})
                            </a>
                            <button class="btn btn-gold-outline" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ $suspiciousActivity['total_flagged'] }}</h3>
                            <p class="mb-0">Flagged Submissions</p>
                        </div>
                        <i class="bi bi-flag display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ $suspiciousActivity['rapid_submissions'] }}</h3>
                            <p class="mb-0">Rapid Submissions</p>
                        </div>
                        <i class="bi bi-lightning display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ $suspiciousActivity['large_changes'] }}</h3>
                            <p class="mb-0">Large Vote Changes</p>
                        </div>
                        <i class="bi bi-arrow-up-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ $suspiciousActivity['agents_with_multiple_submissions'] }}</h3>
                            <p class="mb-0">Multiple Submissions</p>
                        </div>
                        <i class="bi bi-people display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Agent, station, candidate...">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="flagged" {{ request('status') == 'flagged' ? 'selected' : '' }}>Flagged</option>
                        <option value="verified" {{ request('status') == 'verified' ? 'selected' : '' }}>Verified</option>
                        <option value="unverified" {{ request('status') == 'unverified' ? 'selected' : '' }}>Unverified</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="agent_id" class="form-label">Agent</label>
                    <select class="form-select" id="agent_id" name="agent_id">
                        <option value="">All Agents</option>
                        @foreach($agents as $agent)
                            <option value="{{ $agent->id }}" {{ request('agent_id') == $agent->id ? 'selected' : '' }}>
                                {{ $agent->user->name ?? 'Unknown' }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">To Date</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Audit Logs Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Audit Trail
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Submission Time</th>
                            <th>Agent</th>
                            <th>Station</th>
                            <th>Candidate</th>
                            <th>Vote Change</th>
                            <th>Method</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($auditLogs as $log)
                        <tr class="{{ $log->is_flagged ? 'table-danger' : ($log->is_verified ? 'table-success' : '') }}">
                            <td>
                                <div class="fw-semibold">{{ $log->formatted_submission_time }}</div>
                                <small class="text-muted">{{ $log->submission_time->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div>{{ $log->agent->user->name ?? 'Unknown' }}</div>
                                <small class="text-muted">{{ $log->agent->user->phone_number ?? 'N/A' }}</small>
                            </td>
                            <td>
                                <div>{{ $log->pollingStation->name }}</div>
                                <small class="text-muted">{{ $log->pollingStation->district }}</small>
                            </td>
                            <td>
                                <div>{{ $log->candidate->name }}</div>
                                <small class="text-muted">{{ $log->candidate->position->name }}</small>
                            </td>
                            <td>
                                <div class="fw-bold {{ $log->vote_difference > 0 ? 'text-success' : ($log->vote_difference < 0 ? 'text-danger' : 'text-muted') }}">
                                    @if($log->action_type === 'create')
                                        <span class="badge bg-primary">NEW</span> {{ $log->new_votes }}
                                    @else
                                        @if($log->vote_difference > 0)
                                            +{{ $log->vote_difference }}
                                        @elseif($log->vote_difference < 0)
                                            {{ $log->vote_difference }}
                                        @else
                                            No change
                                        @endif
                                    @endif
                                </div>
                                <small class="text-muted">Total: {{ $log->new_votes }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->submission_method === 'api' ? 'info' : ($log->submission_method === 'manager_portal' ? 'warning' : 'secondary') }}">
                                    {{ ucfirst(str_replace('_', ' ', $log->submission_method)) }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->status_badge_color }}">
                                    {{ $log->status_text }}
                                </span>
                                @if($log->is_flagged)
                                    <div class="mt-1">
                                        <small class="text-danger">{{ ucfirst(str_replace('_', ' ', $log->flag_reason)) }}</small>
                                    </div>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    @if(!$log->is_verified)
                                        <button class="btn btn-sm btn-success" 
                                                onclick="verifySubmission({{ $log->id }})" 
                                                title="Verify">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                    @endif
                                    
                                    @if(!$log->is_flagged)
                                        <button class="btn btn-sm btn-warning" 
                                                onclick="flagSubmission({{ $log->id }})" 
                                                title="Flag">
                                            <i class="bi bi-flag"></i>
                                        </button>
                                    @else
                                        <button class="btn btn-sm btn-secondary" 
                                                onclick="unflagSubmission({{ $log->id }})" 
                                                title="Unflag">
                                            <i class="bi bi-flag-fill"></i>
                                        </button>
                                    @endif
                                    
                                    <a href="{{ route('admin.audit.agent.performance', $log->agent) }}"
                                       class="btn btn-sm btn-primary"
                                       title="Agent Performance">
                                        <i class="bi bi-speedometer2"></i>
                                    </a>

                                    <button class="btn btn-sm btn-info"
                                            onclick="viewDetails({{ $log->id }})"
                                            title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">No audit logs found</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($auditLogs->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing {{ $auditLogs->firstItem() }} to {{ $auditLogs->lastItem() }} 
                    of {{ $auditLogs->total() }} audit logs
                </div>
                <div>
                    {{ $auditLogs->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Modals will be added here -->
<div id="modalContainer"></div>

<script>
function refreshData() {
    window.location.reload();
}

function verifySubmission(logId) {
    // Implementation for verify modal
    console.log('Verify submission:', logId);
}

function flagSubmission(logId) {
    // Implementation for flag modal
    console.log('Flag submission:', logId);
}

function unflagSubmission(logId) {
    // Implementation for unflag modal
    console.log('Unflag submission:', logId);
}

function viewDetails(logId) {
    // Implementation for details modal
    console.log('View details:', logId);
}
</script>
@endsection
