@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-flag text-warning me-2"></i>
                        Flagged Submissions
                    </h2>
                    <p class="text-muted mb-0">Review and manage submissions that have been flagged for suspicious activity</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.audit.activity') }}" class="btn btn-outline-success">
                        <i class="bi bi-activity me-1"></i>
                        Live Activity
                    </a>
                    <a href="{{ route('admin.audit.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Audit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-warning shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-flag text-warning fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $suspiciousActivity['total_flagged'] }}</h3>
                    <p class="text-muted mb-0">Total Flagged</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-danger shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-speedometer2 text-danger fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $suspiciousActivity['rapid_submissions']->count() }}</h3>
                    <p class="text-muted mb-0">Rapid Submissions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-info shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-arrow-up-right text-info fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $suspiciousActivity['large_changes']->count() }}</h3>
                    <p class="text-muted mb-0">Large Changes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-secondary shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-secondary bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-clock text-secondary fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1">{{ $suspiciousActivity['unusual_timing']->count() }}</h3>
                    <p class="text-muted mb-0">Unusual Timing</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Flagged Submissions Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-transparent">
            <h5 class="mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Flagged Submissions Requiring Review
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Submission Time</th>
                            <th>Agent</th>
                            <th>Station</th>
                            <th>Candidate</th>
                            <th>Vote Change</th>
                            <th>Flag Reason</th>
                            <th>Method</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($flaggedSubmissions as $log)
                        <tr class="table-warning">
                            <td>
                                <div class="fw-semibold">{{ $log->submission_time->format('M j, Y H:i') }}</div>
                                <small class="text-muted">{{ $log->submission_time->diffForHumans() }}</small>
                            </td>
                            <td>
                                <div class="fw-semibold">{{ $log->agent->user->name }}</div>
                                <small class="text-muted">{{ $log->agent->user->phone_number }}</small>
                            </td>
                            <td>{{ $log->pollingStation->name }}</td>
                            <td>
                                <div class="fw-semibold">{{ $log->candidate->name }}</div>
                                <small class="text-muted">{{ $log->candidate->position->name }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ number_format($log->new_votes) }}</span>
                                @if($log->vote_difference != 0)
                                    <div class="small text-{{ $log->vote_difference > 0 ? 'success' : 'danger' }}">
                                        {{ $log->vote_difference > 0 ? '+' : '' }}{{ number_format($log->vote_difference) }}
                                    </div>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-warning">{{ $log->flag_reason }}</span>
                                @if($log->flag_notes)
                                    <div class="small text-muted mt-1">{{ $log->flag_notes }}</div>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $log->submission_method === 'api' ? 'success' : 'info' }}">
                                    {{ ucfirst($log->submission_method) }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ route('admin.audit.agent.performance', $log->agent) }}" 
                                       class="btn btn-outline-primary btn-sm" 
                                       title="Agent Performance">
                                        <i class="bi bi-speedometer2"></i>
                                    </a>
                                    
                                    @if(!$log->is_verified)
                                        <button class="btn btn-outline-success btn-sm" 
                                                onclick="verifySubmission({{ $log->id }})" 
                                                title="Verify">
                                            <i class="bi bi-check"></i>
                                        </button>
                                    @endif
                                    
                                    <button class="btn btn-outline-info btn-sm" 
                                            onclick="unflagSubmission({{ $log->id }})" 
                                            title="Remove Flag">
                                        <i class="bi bi-flag-fill"></i>
                                    </button>
                                    
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            onclick="viewDetails({{ $log->id }})" 
                                            title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="bi bi-check-circle display-4 text-success"></i>
                                    <h5 class="mt-3">No Flagged Submissions</h5>
                                    <p class="mb-0">All submissions are currently clean. Great job!</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($flaggedSubmissions->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing {{ $flaggedSubmissions->firstItem() }} to {{ $flaggedSubmissions->lastItem() }} 
                    of {{ $flaggedSubmissions->total() }} flagged submissions
                </div>
                <div>
                    {{ $flaggedSubmissions->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
function verifySubmission(logId) {
    if (confirm('Are you sure you want to verify this submission? This will remove the flag.')) {
        fetch(`/admin/audit/verify/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error verifying submission');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error verifying submission');
        });
    }
}

function unflagSubmission(logId) {
    const notes = prompt('Enter notes for removing this flag (optional):');
    if (notes !== null) { // User didn't cancel
        fetch(`/admin/audit/unflag/${logId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ notes: notes })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error removing flag');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error removing flag');
        });
    }
}

function viewDetails(logId) {
    // This could open a modal with detailed information
    console.log('View details for log ID:', logId);
    // For now, just redirect to the main audit page with a filter
    window.location.href = `/admin/audit?search=${logId}`;
}
</script>
@endsection
