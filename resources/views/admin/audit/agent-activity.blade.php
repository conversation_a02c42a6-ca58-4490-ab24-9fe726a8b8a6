@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-activity text-primary me-2"></i>
                        Agent Activity Dashboard
                    </h2>
                    <p class="text-muted mb-0">Real-time monitoring and analytics of agent submission activities</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        Refresh
                    </button>
                    <a href="{{ route('admin.audit.index') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i>
                        Back to Audit
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Activity Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-clock text-success fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1" id="last5MinutesCount">{{ $realTimeActivity['last_5_minutes'] }}</h3>
                    <p class="text-muted mb-0">Last 5 Minutes</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-hourglass-split text-info fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1" id="lastHourCount">{{ $realTimeActivity['last_hour'] }}</h3>
                    <p class="text-muted mb-0">Last Hour</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-calendar-day text-warning fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1" id="todayCount">{{ $realTimeActivity['today'] }}</h3>
                    <p class="text-muted mb-0">Today</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-people text-primary fs-4"></i>
                        </div>
                    </div>
                    <h3 class="mb-1" id="activeAgentsCount">{{ $realTimeActivity['active_agents_last_hour'] }}</h3>
                    <p class="text-muted mb-0">Active Agents</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-graph-up me-2"></i>
                        Submission Activity (24 Hours)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="submissionChart" height="100"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-pie-chart me-2"></i>
                        Submission Methods
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="methodChart" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity and Top Agents -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>
                        Recent Submissions (Last 30 Minutes)
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Time</th>
                                    <th>Agent</th>
                                    <th>Station</th>
                                    <th>Candidate</th>
                                    <th>Votes</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody id="recentSubmissionsTable">
                                @foreach($realTimeActivity['recent_submissions'] as $submission)
                                <tr class="{{ $submission->is_flagged ? 'table-danger' : '' }}">
                                    <td>
                                        <small class="text-muted">{{ $submission->submission_time->format('H:i:s') }}</small>
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ $submission->agent->user->name }}</div>
                                        <small class="text-muted">{{ $submission->agent->user->phone_number }}</small>
                                    </td>
                                    <td>{{ $submission->pollingStation->name }}</td>
                                    <td>{{ $submission->candidate->name }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ number_format($submission->new_votes) }}</span>
                                        @if($submission->vote_difference != 0)
                                            <small class="text-muted">({{ $submission->vote_difference > 0 ? '+' : '' }}{{ $submission->vote_difference }})</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $submission->submission_method === 'api' ? 'success' : 'info' }}">
                                            {{ ucfirst($submission->submission_method) }}
                                        </span>
                                    </td>
                                    <td>
                                        @if($submission->is_flagged)
                                            <span class="badge bg-danger">Flagged</span>
                                        @elseif($submission->is_verified)
                                            <span class="badge bg-success">Verified</span>
                                        @else
                                            <span class="badge bg-secondary">Pending</span>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">
                        <i class="bi bi-trophy me-2"></i>
                        Most Active Agents
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($analytics['most_active_agents'] as $index => $agent)
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <span class="fw-bold text-primary">{{ $index + 1 }}</span>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="fw-semibold">{{ $agent->agent->user->name }}</div>
                            <small class="text-muted">{{ $agent->agent->user->phone_number }}</small>
                        </div>
                        <div class="flex-shrink-0">
                            <span class="badge bg-primary">{{ $agent->submission_count }}</span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Suspicious Activity Alerts -->
    @if(count($suspiciousPatterns['rapid_submitters']) > 0 || count($suspiciousPatterns['unusual_timing']) > 0)
    <div class="row">
        <div class="col-12">
            <div class="card border-warning shadow-sm">
                <div class="card-header bg-warning bg-opacity-10">
                    <h5 class="mb-0 text-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Suspicious Activity Detected
                    </h5>
                </div>
                <div class="card-body">
                    @if(count($suspiciousPatterns['rapid_submitters']) > 0)
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-speedometer2 me-2"></i>Rapid Submissions Detected</h6>
                        <p class="mb-2">The following agents have made multiple submissions in a short time:</p>
                        <ul class="mb-0">
                            @foreach($suspiciousPatterns['rapid_submitters'] as $submitter)
                            <li>{{ $submitter->agent->user->name }} - {{ $submitter->count }} submissions in last 2 hours</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    @if(count($suspiciousPatterns['unusual_timing']) > 0)
                    <div class="alert alert-info">
                        <h6><i class="bi bi-clock me-2"></i>Unusual Timing Submissions</h6>
                        <p class="mb-2">Recent submissions outside normal hours (6 AM - 10 PM):</p>
                        <ul class="mb-0">
                            @foreach($suspiciousPatterns['unusual_timing']->take(5) as $submission)
                            <li>{{ $submission->agent->user->name }} at {{ $submission->submission_time->format('M j, H:i') }}</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Auto-refresh every 30 seconds
setInterval(refreshData, 30000);

function refreshData() {
    fetch('{{ route("admin.audit.api.activity") }}')
        .then(response => response.json())
        .then(data => {
            // Update counters
            document.getElementById('last5MinutesCount').textContent = data.realTime.last_5_minutes;
            document.getElementById('lastHourCount').textContent = data.realTime.last_hour;
            document.getElementById('todayCount').textContent = data.realTime.today;
            document.getElementById('activeAgentsCount').textContent = data.realTime.active_agents_last_hour;
            
            // Update recent submissions table
            updateRecentSubmissionsTable(data.realTime.recent_submissions);
        })
        .catch(error => console.error('Error refreshing data:', error));
}

function updateRecentSubmissionsTable(submissions) {
    const tbody = document.getElementById('recentSubmissionsTable');
    tbody.innerHTML = '';
    
    submissions.forEach(submission => {
        const row = document.createElement('tr');
        if (submission.is_flagged) {
            row.classList.add('table-danger');
        }
        
        row.innerHTML = `
            <td><small class="text-muted">${new Date(submission.submission_time).toLocaleTimeString()}</small></td>
            <td>
                <div class="fw-semibold">${submission.agent.user.name}</div>
                <small class="text-muted">${submission.agent.user.phone_number}</small>
            </td>
            <td>${submission.polling_station.name}</td>
            <td>${submission.candidate.name}</td>
            <td>
                <span class="badge bg-primary">${submission.new_votes.toLocaleString()}</span>
                ${submission.vote_difference != 0 ? `<small class="text-muted">(${submission.vote_difference > 0 ? '+' : ''}${submission.vote_difference})</small>` : ''}
            </td>
            <td>
                <span class="badge bg-${submission.submission_method === 'api' ? 'success' : 'info'}">
                    ${submission.submission_method.charAt(0).toUpperCase() + submission.submission_method.slice(1)}
                </span>
            </td>
            <td>
                ${submission.is_flagged ? '<span class="badge bg-danger">Flagged</span>' : 
                  submission.is_verified ? '<span class="badge bg-success">Verified</span>' : 
                  '<span class="badge bg-secondary">Pending</span>'}
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    // Submission activity chart
    const submissionCtx = document.getElementById('submissionChart').getContext('2d');
    new Chart(submissionCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode(array_keys($analytics['submissions_by_hour'])) !!}.map(hour => hour + ':00'),
            datasets: [{
                label: 'Submissions',
                data: {!! json_encode(array_values($analytics['submissions_by_hour'])) !!},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Submission methods chart
    const methodCtx = document.getElementById('methodChart').getContext('2d');
    new Chart(methodCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode(array_keys($analytics['submission_methods'])) !!}.map(method => method.charAt(0).toUpperCase() + method.slice(1)),
            datasets: [{
                data: {!! json_encode(array_values($analytics['submission_methods'])) !!},
                backgroundColor: [
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
</script>
@endsection
