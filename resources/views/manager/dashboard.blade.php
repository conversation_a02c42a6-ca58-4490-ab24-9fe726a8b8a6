@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header-gold">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 text-white">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Polling Manager Dashboard
                            </h2>
                            <p class="text-white opacity-75 mb-0">Manage vote submissions and evidence for all polling stations</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-gold-outline" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card card-custom hover-lift">
                <div class="card-header-gold">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0 text-white">{{ number_format($totalStations) }}</h3>
                            <p class="mb-0 text-white opacity-75">Total Stations</p>
                        </div>
                        <i class="bi bi-building display-6 text-white opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-custom hover-lift">
                <div class="card-body-compact">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1 text-gold">{{ number_format($submittedStations) }}</h3>
                            <p class="mb-0 text-muted fw-medium">Submitted</p>
                            <div class="progress mt-2" style="height: 4px;">
                                <div class="progress-bar bg-gold" style="width: {{ $totalStations > 0 ? ($submittedStations / $totalStations) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                        <div class="text-gold" style="font-size: 2.5rem; opacity: 0.7;">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-custom hover-lift">
                <div class="card-body-compact">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1 text-warning">{{ number_format($pendingStations) }}</h3>
                            <p class="mb-0 text-muted fw-medium">Pending</p>
                            <div class="progress mt-2" style="height: 4px;">
                                <div class="progress-bar bg-warning" style="width: {{ $totalStations > 0 ? ($pendingStations / $totalStations) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                        <div class="text-warning" style="font-size: 2.5rem; opacity: 0.7;">
                            <i class="bi bi-clock"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card card-custom hover-lift">
                <div class="card-body-compact">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-1 text-info">{{ number_format($totalVotes) }}</h3>
                            <p class="mb-0 text-muted fw-medium">Total Votes</p>
                            <small class="text-muted">
                                <i class="bi bi-graph-up me-1"></i>
                                Across all stations
                            </small>
                        </div>
                        <div class="text-info" style="font-size: 2.5rem; opacity: 0.7;">
                            <i class="bi bi-bar-chart"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card card-custom mb-4">
        <div class="card-header-light">
            <h6 class="mb-0 text-gold">
                <i class="bi bi-funnel me-2"></i>
                Filter Polling Stations
            </h6>
        </div>
        <div class="card-body-compact">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label-custom">
                        <i class="bi bi-search me-1"></i>Search
                    </label>
                    <input type="text" class="form-control-custom" id="search" name="search"
                           value="{{ request('search') }}" placeholder="Search stations...">
                </div>
                <div class="col-md-2">
                    <label for="district" class="form-label-custom">
                        <i class="bi bi-geo-alt me-1"></i>District
                    </label>
                    <select class="form-control-custom" id="district" name="district">
                        <option value="">All Districts</option>
                        @foreach($districts as $dist)
                            <option value="{{ $dist }}" {{ request('district') == $dist ? 'selected' : '' }}>
                                {{ $dist }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="county" class="form-label-custom">
                        <i class="bi bi-map me-1"></i>County
                    </label>
                    <select class="form-control-custom" id="county" name="county">
                        <option value="">All Counties</option>
                        @foreach($counties as $cnty)
                            <option value="{{ $cnty }}" {{ request('county') == $cnty ? 'selected' : '' }}>
                                {{ $cnty }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="subcounty" class="form-label-custom">
                        <i class="bi bi-pin-map me-1"></i>Subcounty
                    </label>
                    <select class="form-control-custom" id="subcounty" name="subcounty">
                        <option value="">All Subcounties</option>
                        @foreach($subcounties as $sub)
                            <option value="{{ $sub }}" {{ request('subcounty') == $sub ? 'selected' : '' }}>
                                {{ $sub }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label-custom">
                        <i class="bi bi-check-circle me-1"></i>Status
                    </label>
                    <select class="form-control-custom" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="submitted" {{ request('status') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-gold w-100">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Polling Stations Table -->
    <div class="card card-custom">
        <div class="card-header-gold">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-white">
                    <i class="bi bi-list-ul me-2"></i>
                    Polling Stations Management
                </h5>
                <div class="d-flex gap-2">
                    <span class="badge badge-outline text-white">{{ $pollingStations->total() }} stations</span>
                    <button class="btn btn-gold-outline btn-compact" onclick="exportData()">
                        <i class="bi bi-download me-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-custom mb-0">
                    <thead>
                        <tr>
                            <th>
                                <i class="bi bi-building me-1"></i>Station
                            </th>
                            <th>
                                <i class="bi bi-geo-alt me-1"></i>Location
                            </th>
                            <th>
                                <i class="bi bi-person me-1"></i>Agent
                            </th>
                            <th>
                                <i class="bi bi-check-circle me-1"></i>Status
                            </th>
                            <th>
                                <i class="bi bi-bar-chart me-1"></i>Votes
                            </th>
                            <th>
                                <i class="bi bi-camera me-1"></i>Evidence
                            </th>
                            <th>
                                <i class="bi bi-gear me-1"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($pollingStations as $station)
                        <tr class="hover-lift">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <div class="bg-gold rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 40px; height: 40px;">
                                            <i class="bi bi-building text-white"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-dark">{{ $station->name }}</div>
                                        <small class="text-muted">ID: {{ $station->id }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="fw-medium text-dark">{{ $station->district }}</div>
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt me-1"></i>{{ $station->county }}, {{ $station->subcounty }}
                                </small>
                            </td>
                            <td>
                                @if($station->agent && $station->agent->user)
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            <div class="bg-gold rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 32px; height: 32px;">
                                                <span class="text-white fw-bold" style="font-size: 0.8rem;">
                                                    {{ substr($station->agent->user->name, 0, 1) }}
                                                </span>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-medium">{{ $station->agent->user->name }}</div>
                                            <small class="text-muted">{{ $station->agent->user->phone_number }}</small>
                                        </div>
                                    </div>
                                @else
                                    <div class="text-muted">
                                        <i class="bi bi-person-x me-1"></i>No agent assigned
                                    </div>
                                @endif
                            </td>
                            <td>
                                @if($station->agent && $station->agent->votes->count() > 0)
                                    <span class="badge badge-gold">
                                        <i class="bi bi-check-circle me-1"></i>Submitted
                                    </span>
                                @else
                                    <span class="badge bg-warning">
                                        <i class="bi bi-clock me-1"></i>Pending
                                    </span>
                                @endif
                            </td>
                            <td>
                                @if($station->agent)
                                    <div class="text-center">
                                        <div class="fw-bold text-gold" style="font-size: 1.2rem;">
                                            {{ number_format($station->agent->votes->sum('number_of_votes')) }}
                                        </div>
                                        <small class="text-muted">votes</small>
                                    </div>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($station->agent)
                                    <span class="badge bg-info">
                                        <i class="bi bi-camera me-1"></i>{{ $station->agent->eveidences->count() }} files
                                    </span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <a href="{{ route('manager.vote-form', $station) }}"
                                       class="btn btn-compact btn-gold" title="Submit Votes">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="{{ route('manager.evidence-form', $station) }}"
                                       class="btn btn-compact btn-gold-outline" title="Upload Evidence">
                                        <i class="bi bi-upload"></i>
                                    </a>
                                    <button class="btn btn-compact btn-outline-info"
                                            onclick="viewStationDetails({{ $station->id }})" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">No polling stations found</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($pollingStations->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing {{ $pollingStations->firstItem() }} to {{ $pollingStations->lastItem() }} 
                    of {{ $pollingStations->total() }} stations
                </div>
                <div>
                    {{ $pollingStations->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Station Details Modal -->
<div class="modal fade" id="stationDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Station Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="stationDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshData() {
    window.location.reload();
}

function viewStationDetails(stationId) {
    const modal = new bootstrap.Modal(document.getElementById('stationDetailsModal'));
    const content = document.getElementById('stationDetailsContent');
    
    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // Fetch station details
    fetch(`/manager/station/${stationId}/details`)
        .then(response => response.json())
        .then(data => {
            content.innerHTML = generateStationDetailsHTML(data);
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Failed to load station details</div>';
        });
}

function generateStationDetailsHTML(data) {
    const station = data.station;
    const voteSummary = data.vote_summary;
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>Station Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${station.name}</td></tr>
                    <tr><td><strong>District:</strong></td><td>${station.district}</td></tr>
                    <tr><td><strong>County:</strong></td><td>${station.county}</td></tr>
                    <tr><td><strong>Subcounty:</strong></td><td>${station.subcounty}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Agent Information</h6>
                ${station.agent ? `
                    <table class="table table-sm">
                        <tr><td><strong>Name:</strong></td><td>${station.agent.user.name}</td></tr>
                        <tr><td><strong>Phone:</strong></td><td>${station.agent.user.phone_number}</td></tr>
                        <tr><td><strong>Evidence Files:</strong></td><td>${data.evidence_count}</td></tr>
                    </table>
                ` : '<p class="text-muted">No agent assigned</p>'}
            </div>
        </div>
    `;
    
    if (Object.keys(voteSummary).length > 0) {
        html += '<hr><h6>Vote Summary</h6>';
        for (const [position, summary] of Object.entries(voteSummary)) {
            html += `
                <div class="mb-3">
                    <strong>${position}</strong> (Total: ${summary.total_votes})
                    <ul class="list-unstyled ms-3">
            `;
            summary.candidates.forEach(candidate => {
                html += `<li>${candidate.name}: ${candidate.votes} votes</li>`;
            });
            html += '</ul></div>';
        }
    }
    
    return html;
}
</script>
@endsection

@section('styles')
<style>
/* Enhanced Manager Dashboard Styles */
.progress {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}

.progress-bar.bg-gold {
    background: var(--gold-gradient) !important;
}

.badge-gold {
    background: var(--gold-gradient);
    color: white;
    font-weight: var(--font-weight-medium);
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
}

/* Enhanced hover effects for table rows */
.table-custom tbody tr.hover-lift:hover {
    background: var(--gold-gradient-light);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 165, 0, 0.15);
}

/* Animated statistics cards */
.card.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(255, 165, 0, 0.15);
}

/* Enhanced modal styling */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    background: var(--gold-gradient);
    color: white;
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-header .btn-close {
    filter: brightness(0) invert(1);
}

/* Loading animation */
.spinner-border {
    color: var(--primary-color);
}

/* Enhanced button group */
.btn-group .btn + .btn {
    margin-left: 2px;
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .card-header-gold .d-flex {
        flex-direction: column;
        gap: 1rem;
    }

    .table-custom {
        font-size: 0.875rem;
    }

    .btn-compact {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Pulse animation for pending status */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.badge.bg-warning {
    animation: pulse 2s infinite;
}

/* Enhanced export button */
.btn-gold-outline:hover {
    background: var(--gold-gradient);
    color: white;
    transform: translateY(-1px);
}
</style>
@endsection

@section('scripts')
<script>
// Enhanced dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh functionality
    let autoRefreshInterval;

    function startAutoRefresh() {
        autoRefreshInterval = setInterval(() => {
            // Only refresh if user is not actively interacting
            if (document.hidden === false) {
                refreshData();
            }
        }, 300000); // 5 minutes
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    // Start auto-refresh
    startAutoRefresh();

    // Stop auto-refresh when user is interacting
    document.addEventListener('click', stopAutoRefresh);
    document.addEventListener('keydown', stopAutoRefresh);

    // Restart auto-refresh after 30 seconds of inactivity
    let inactivityTimer;
    function resetInactivityTimer() {
        clearTimeout(inactivityTimer);
        inactivityTimer = setTimeout(startAutoRefresh, 30000);
    }

    document.addEventListener('click', resetInactivityTimer);
    document.addEventListener('keydown', resetInactivityTimer);

    // Enhanced search functionality
    const searchInput = document.getElementById('search');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // Auto-submit form after 1 second of no typing
                this.form.submit();
            }, 1000);
        });
    }

    // Add loading states to buttons
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
                submitBtn.disabled = true;
            }
        });
    });
});

// Enhanced export functionality
function exportData() {
    const exportBtn = event.target;
    const originalContent = exportBtn.innerHTML;

    exportBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Exporting...';
    exportBtn.disabled = true;

    // Simulate export process
    setTimeout(() => {
        exportBtn.innerHTML = '<i class="bi bi-check me-1"></i>Exported!';
        setTimeout(() => {
            exportBtn.innerHTML = originalContent;
            exportBtn.disabled = false;
        }, 2000);
    }, 1500);

    // Here you would implement actual export functionality
    console.log('Exporting polling station data...');
}

// Enhanced refresh with visual feedback
function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spinning me-1"></i>Refreshing...';
        refreshBtn.disabled = true;
    }

    // Add spinning animation
    const style = document.createElement('style');
    style.textContent = `
        .spinning {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Enhanced station details modal
function viewStationDetails(stationId) {
    const modal = new bootstrap.Modal(document.getElementById('stationDetailsModal'));
    const content = document.getElementById('stationDetailsContent');

    // Enhanced loading animation
    content.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-gold" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-3 text-muted">Loading station details...</div>
        </div>
    `;

    modal.show();

    // Simulate API call (replace with actual endpoint)
    setTimeout(() => {
        content.innerHTML = `
            <div class="alert alert-gold">
                <i class="bi bi-info-circle me-2"></i>
                Station details functionality will be implemented with backend API.
            </div>
            <div class="text-center">
                <h5 class="text-gold">Station ID: ${stationId}</h5>
                <p class="text-muted">Detailed information will be displayed here.</p>
            </div>
        `;
    }, 1000);
}
</script>
@endsection
