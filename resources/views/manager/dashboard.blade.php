@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-custom">
                <div class="card-header-gold">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 text-white">
                                <i class="bi bi-speedometer2 me-2"></i>
                                Polling Manager Dashboard
                            </h2>
                            <p class="text-white opacity-75 mb-0">Manage vote submissions and evidence for all polling stations</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-gold-outline" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card card-custom hover-lift">
                <div class="card-header-gold">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0 text-white">{{ number_format($totalStations) }}</h3>
                            <p class="mb-0 text-white opacity-75">Total Stations</p>
                        </div>
                        <i class="bi bi-building display-6 text-white opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format($submittedStations) }}</h3>
                            <p class="mb-0">Submitted</p>
                        </div>
                        <i class="bi bi-check-circle display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format($pendingStations) }}</h3>
                            <p class="mb-0">Pending</p>
                        </div>
                        <i class="bi bi-clock display-6"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="mb-0">{{ number_format($totalVotes) }}</h3>
                            <p class="mb-0">Total Votes</p>
                        </div>
                        <i class="bi bi-bar-chart display-6"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Search stations...">
                </div>
                <div class="col-md-2">
                    <label for="district" class="form-label">District</label>
                    <select class="form-select" id="district" name="district">
                        <option value="">All Districts</option>
                        @foreach($districts as $dist)
                            <option value="{{ $dist }}" {{ request('district') == $dist ? 'selected' : '' }}>
                                {{ $dist }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="county" class="form-label">County</label>
                    <select class="form-select" id="county" name="county">
                        <option value="">All Counties</option>
                        @foreach($counties as $cnty)
                            <option value="{{ $cnty }}" {{ request('county') == $cnty ? 'selected' : '' }}>
                                {{ $cnty }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="subcounty" class="form-label">Subcounty</label>
                    <select class="form-select" id="subcounty" name="subcounty">
                        <option value="">All Subcounties</option>
                        @foreach($subcounties as $sub)
                            <option value="{{ $sub }}" {{ request('subcounty') == $sub ? 'selected' : '' }}>
                                {{ $sub }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="submitted" {{ request('status') == 'submitted' ? 'selected' : '' }}>Submitted</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Polling Stations Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Polling Stations
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Station</th>
                            <th>Location</th>
                            <th>Agent</th>
                            <th>Status</th>
                            <th>Votes</th>
                            <th>Evidence</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($pollingStations as $station)
                        <tr>
                            <td>
                                <div class="fw-semibold">{{ $station->name }}</div>
                                <small class="text-muted">ID: {{ $station->id }}</small>
                            </td>
                            <td>
                                <div>{{ $station->district }}</div>
                                <small class="text-muted">{{ $station->county }}, {{ $station->subcounty }}</small>
                            </td>
                            <td>
                                @if($station->agent && $station->agent->user)
                                    <div>{{ $station->agent->user->name }}</div>
                                    <small class="text-muted">{{ $station->agent->user->phone_number }}</small>
                                @else
                                    <span class="text-muted">No agent assigned</span>
                                @endif
                            </td>
                            <td>
                                @if($station->agent && $station->agent->votes->count() > 0)
                                    <span class="badge bg-success">Submitted</span>
                                @else
                                    <span class="badge bg-warning">Pending</span>
                                @endif
                            </td>
                            <td>
                                @if($station->agent)
                                    <span class="fw-bold">{{ $station->agent->votes->sum('number_of_votes') }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($station->agent)
                                    <span class="badge bg-info">{{ $station->agent->eveidences->count() }} files</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('manager.vote-form', $station) }}" 
                                       class="btn btn-sm btn-primary" title="Submit Votes">
                                        <i class="bi bi-pencil-square"></i>
                                    </a>
                                    <a href="{{ route('manager.evidence-form', $station) }}" 
                                       class="btn btn-sm btn-success" title="Upload Evidence">
                                        <i class="bi bi-upload"></i>
                                    </a>
                                    <button class="btn btn-sm btn-info" 
                                            onclick="viewStationDetails({{ $station->id }})" title="View Details">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-2">No polling stations found</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        
        @if($pollingStations->hasPages())
        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small">
                    Showing {{ $pollingStations->firstItem() }} to {{ $pollingStations->lastItem() }} 
                    of {{ $pollingStations->total() }} stations
                </div>
                <div>
                    {{ $pollingStations->links() }}
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<!-- Station Details Modal -->
<div class="modal fade" id="stationDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Station Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="stationDetailsContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function refreshData() {
    window.location.reload();
}

function viewStationDetails(stationId) {
    const modal = new bootstrap.Modal(document.getElementById('stationDetailsModal'));
    const content = document.getElementById('stationDetailsContent');
    
    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // Fetch station details
    fetch(`/manager/station/${stationId}/details`)
        .then(response => response.json())
        .then(data => {
            content.innerHTML = generateStationDetailsHTML(data);
        })
        .catch(error => {
            content.innerHTML = '<div class="alert alert-danger">Failed to load station details</div>';
        });
}

function generateStationDetailsHTML(data) {
    const station = data.station;
    const voteSummary = data.vote_summary;
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>Station Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${station.name}</td></tr>
                    <tr><td><strong>District:</strong></td><td>${station.district}</td></tr>
                    <tr><td><strong>County:</strong></td><td>${station.county}</td></tr>
                    <tr><td><strong>Subcounty:</strong></td><td>${station.subcounty}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Agent Information</h6>
                ${station.agent ? `
                    <table class="table table-sm">
                        <tr><td><strong>Name:</strong></td><td>${station.agent.user.name}</td></tr>
                        <tr><td><strong>Phone:</strong></td><td>${station.agent.user.phone_number}</td></tr>
                        <tr><td><strong>Evidence Files:</strong></td><td>${data.evidence_count}</td></tr>
                    </table>
                ` : '<p class="text-muted">No agent assigned</p>'}
            </div>
        </div>
    `;
    
    if (Object.keys(voteSummary).length > 0) {
        html += '<hr><h6>Vote Summary</h6>';
        for (const [position, summary] of Object.entries(voteSummary)) {
            html += `
                <div class="mb-3">
                    <strong>${position}</strong> (Total: ${summary.total_votes})
                    <ul class="list-unstyled ms-3">
            `;
            summary.candidates.forEach(candidate => {
                html += `<li>${candidate.name}: ${candidate.votes} votes</li>`;
            });
            html += '</ul></div>';
        }
    }
    
    return html;
}
</script>
@endsection
