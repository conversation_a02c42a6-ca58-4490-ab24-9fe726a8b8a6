@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="bi bi-pencil-square text-primary me-2"></i>
                        Submit Votes
                    </h2>
                    <p class="text-muted mb-0">{{ $station->name }} - {{ $station->district }}</p>
                </div>
                <a href="{{ route('manager.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-1"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Station Info Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">Station Information</h6>
                    <table class="table table-sm table-borderless">
                        <tr><td><strong>Name:</strong></td><td>{{ $station->name }}</td></tr>
                        <tr><td><strong>District:</strong></td><td>{{ $station->district }}</td></tr>
                        <tr><td><strong>County:</strong></td><td>{{ $station->county }}</td></tr>
                        <tr><td><strong>Subcounty:</strong></td><td>{{ $station->subcounty }}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">Agent Information</h6>
                    @if($agent && $agent->user)
                        <table class="table table-sm table-borderless">
                            <tr><td><strong>Name:</strong></td><td>{{ $agent->user->name }}</td></tr>
                            <tr><td><strong>Phone:</strong></td><td>{{ $agent->user->phone_number }}</td></tr>
                        </table>
                    @else
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            No agent assigned to this station
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @if(!$agent)
        <div class="alert alert-danger">
            <h6><i class="bi bi-x-circle me-2"></i>Cannot Submit Votes</h6>
            <p class="mb-0">This polling station does not have an agent assigned. Please assign an agent before submitting votes.</p>
        </div>
    @else
        <!-- Vote Submission Form -->
        <form action="{{ route('manager.submit-votes', $station) }}" method="POST" id="voteForm">
            @csrf
            
            @foreach($positions as $position)
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-award text-warning me-2"></i>
                        {{ $position->name }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($position->candidates as $candidate)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="candidate-card">
                                <div class="d-flex align-items-center">
                                    @if($candidate->picture)
                                        <img src="{{ asset('files/' . $candidate->picture) }}" 
                                             alt="{{ $candidate->name }}" 
                                             class="candidate-photo me-3">
                                    @else
                                        <div class="candidate-placeholder me-3">
                                            <i class="bi bi-person"></i>
                                        </div>
                                    @endif
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ $candidate->name }}</h6>
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-hash"></i>
                                            </span>
                                            <input type="number" 
                                                   class="form-control vote-input" 
                                                   name="votes[{{ $candidate->id }}]" 
                                                   value="{{ $existingVotes[$candidate->id] ?? 0 }}"
                                                   min="0" 
                                                   placeholder="0"
                                                   data-position="{{ $position->id }}"
                                                   onchange="updatePositionTotal({{ $position->id }})">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Position Total -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span><strong>Total votes for {{ $position->name }}:</strong></span>
                                    <span class="badge bg-primary fs-6" id="total-{{ $position->id }}">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach



            <!-- Submit Button -->
            <div class="card">
                <div class="card-body text-center">
                    <button type="submit" class="btn btn-primary btn-lg px-5">
                        <i class="bi bi-check-circle me-2"></i>
                        Submit All Votes
                    </button>
                    <div class="mt-2">
                        <small class="text-muted">
                            This will update all vote counts for {{ $station->name }}
                        </small>
                    </div>
                </div>
            </div>
        </form>
    @endif
</div>

<style>
.candidate-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.candidate-card:hover {
    border-color: #0d6efd;
    background: #fff;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.candidate-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #dee2e6;
}

.candidate-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #dee2e6;
}

.vote-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}
</style>

<script>
// Calculate position totals
function updatePositionTotal(positionId) {
    const inputs = document.querySelectorAll(`input[data-position="${positionId}"]`);
    let total = 0;
    
    inputs.forEach(input => {
        const value = parseInt(input.value) || 0;
        total += value;
    });
    
    document.getElementById(`total-${positionId}`).textContent = total.toLocaleString();
}

// Initialize totals on page load
document.addEventListener('DOMContentLoaded', function() {
    @foreach($positions as $position)
        updatePositionTotal({{ $position->id }});
    @endforeach
});

// Form validation
document.getElementById('voteForm').addEventListener('submit', function(e) {
    const inputs = document.querySelectorAll('.vote-input');
    let hasVotes = false;
    
    inputs.forEach(input => {
        if (parseInt(input.value) > 0) {
            hasVotes = true;
        }
    });
    
    if (!hasVotes) {
        e.preventDefault();
        alert('Please enter at least one vote before submitting.');
        return false;
    }
    
    // Confirm submission
    if (!confirm('Are you sure you want to submit these votes? This will update the records for {{ $station->name }}.')) {
        e.preventDefault();
        return false;
    }
});
</script>
@endsection
